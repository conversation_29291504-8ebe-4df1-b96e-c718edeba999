# 有赞Excel导入功能测试指南

## 测试前准备

### 1. 确保门店名称映射表已配置
在使用导入功能前，需要确保门店名称映射表(`oa_store_name_mapping`)中已经配置了有赞门店名称到OA系统门店ID的映射关系。

**重要字段说明**：
- `department_id`: 对应OA系统中oa_department表的门店ID
- `youzan_name`: 有赞平台的门店名称
- `oa_name`: OA系统的门店名称

**配置示例**：
```sql
INSERT INTO oa_store_name_mapping (department_id, oa_name, youzan_name) 
VALUES (1, 'OA门店名称', '有赞门店名称');
```

### 2. 准备测试Excel文件
Excel文件应包含以下4个Sheet：
- 储值跨店结算明细
- 次卡跨店结算明细  
- 网店订单跨店核销明细
- 跨店账单汇总表

## 测试步骤

### 1. 访问跨店结算汇总表页面
- 登录OA系统
- 导航到：跨店结算 → 跨店结算汇总表
- 确认页面正常加载

### 2. 点击导入按钮
- 在表格工具栏找到"导入有赞数据"按钮
- 点击按钮，应该弹出导入弹窗

### 3. 选择导入月份
- 在弹窗中选择要导入的月份（格式：YYYY-MM）
- 确认月份说明显示正确

### 4. 上传Excel文件
- 点击"选择文件"按钮
- 选择准备好的有赞Excel文件
- 确认文件上传成功提示

### 5. 确认导入
- 点击"确认导入"按钮
- 等待导入完成
- 查看导入结果提示

## 常见问题排查

### 1. 500错误 - trim()类型错误
**问题**: `trim() expects parameter 1 to be string, int given`
**解决**: 已通过`strval()`转换修复，确保所有Excel数据都转换为字符串

### 2. 门店名称未找到映射
**问题**: 导入时提示某个门店名称未找到映射
**解决**: 
- 检查门店名称映射表(`oa_store_name_mapping`)是否包含该门店
- 系统支持智能门店名称匹配，包括：
  - **精确匹配**: 完全相同的门店名称
  - **括号转换**: 自动转换中英文括号 `()` ↔ `（）`
  - **空格忽略**: 忽略门店名称中的空格差异
  - **模糊匹配**: 基于包含关系的模糊匹配
- 支持的门店名称字段：
  - 有赞门店名称 (`youzan_name`)
  - OA门店名称 (`oa_name`) 
  - 部门表门店名称 (`oa_department.title`)

**示例匹配规则**:
- `仲正堂推拿艾灸(融创精彩天地店)` ↔ `仲正堂推拿艾灸（融创精彩天地店）`
- `仲正堂推拿艾灸 (太合店)` ↔ `仲正堂推拿艾灸(太合店)`

### 3. 时间格式错误
**问题**: Excel中的时间格式无法解析
**解决**: 
- 检查Excel中的时间格式是否标准
- 系统会自动跳过无法解析的时间，设为null

### 4. Sheet名称不匹配
**问题**: Excel中的Sheet名称与系统期望的不一致
**解决**: 
- 确保Excel包含以下4个Sheet：
  - 储值跨店结算明细
  - 次卡跨店结算明细
  - 网店订单跨店核销明细
  - 跨店账单汇总表

### 5. 跨店账单汇总表合并单元格处理
**特殊说明**: 跨店账单汇总表包含合并单元格，系统已自动处理：
- **表头处理**: 自动跳过前两行表头
- **合并单元格**: 自动处理门店列的合并单元格（如"总部"对应多个他店）
- **汇总行跳过**: 自动跳过"汇总"行，只导入明细数据
- **数据映射**: 29个字段完整映射到数据库表结构

## 验证导入结果

### 1. 检查数据库表
导入成功后，检查以下表是否有新增数据：
- `oa_cross_store_settle_detail` - 储值跨店结算明细
- `oa_cross_store_settle_card_detail` - 次卡跨店结算明细
- `oa_cross_store_settle_online_detail` - 网店订单跨店核销明细
- `oa_cross_store_settle_summary` - 跨店结算汇总表

### 2. 检查数据完整性
- 确认导入的数据量与Excel中的数据量一致
- 检查门店ID是否正确映射
- 验证金额、数量等数值字段是否正确

### 3. 检查日志
查看系统日志确认导入过程：
- 成功日志：`[跨店结算]-[跨店结算汇总表]：有赞Excel数据导入成功`
- 错误日志：查看具体的错误信息

## 性能优化说明

### 批量插入优化
系统已优化为批量插入模式，每100条记录批量插入一次，大幅提升导入效率：
- **批量大小**: 100条记录/批次
- **预期性能**: 1000条记录约需10-30秒（取决于服务器性能）
- **内存优化**: 避免一次性加载所有数据到内存

### 超时处理
如果遇到504超时错误：
1. **检查数据量**: 建议单次导入不超过5000条记录
2. **分批导入**: 大文件可分成多个小文件分批导入
3. **服务器配置**: 可适当调整PHP执行时间限制

## 性能测试

### 1. 大文件测试
- 测试包含大量数据的Excel文件导入
- 观察导入时间和内存使用情况
- 建议测试数据量：100条、500条、1000条、3000条

### 2. 并发测试
- 测试多用户同时导入的情况
- 确认数据不会冲突
- 验证事务隔离性

## 回滚测试

### 1. 错误数据回滚
- 故意提供包含错误数据的Excel文件
- 确认导入失败时数据能正确回滚

### 2. 部分失败处理
- 测试部分数据有问题的情况
- 确认错误处理机制正常工作