# 有赞Excel导入功能开发任务列表

## 任务概述
从有赞平台导出的Excel文件包含四个sheet，需要一次性导入到系统的四张数据库表中。

## 数据表映射关系
- **储值跨店结算明细** → `oa_cross_store_settle_detail`
- **次卡跨店结算明细** → `oa_cross_store_settle_card_detail`  
- **网店订单跨店核销明细** → `oa_cross_store_settle_online_detail`
- **跨店账单汇总表** → `oa_cross_store_settle_summary`

## 开发任务列表

### 1. 前端界面修改
- [x] 在跨店结算汇总表工具栏添加"导入"按钮
- [x] 创建导入弹窗界面，支持文件上传
- [x] 添加导入进度提示和结果反馈

### 2. 后端控制器开发
- [x] 在Summary控制器添加import方法
- [x] 实现Excel文件上传和解析
- [x] 实现四个sheet的数据处理逻辑
- [x] 添加数据验证和错误处理

### 3. 门店名称映射功能
- [x] 利用现有StoreNameMapping表实现门店名称映射
- [x] 在导入过程中自动匹配门店ID
- [x] 处理未匹配门店的错误提示

### 4. 数据处理逻辑
- [x] 解析储值跨店结算明细数据
- [x] 解析次卡跨店结算明细数据
- [x] 解析网店订单跨店核销明细数据
- [x] 解析跨店账单汇总表数据（跳过合并单元格）

### 5. 错误处理和日志
- [x] 添加详细的错误日志记录
- [x] 实现导入结果统计
- [x] 提供友好的错误提示信息

## 技术要点

### Excel解析
- 使用PhpSpreadsheet库解析Excel文件
- 支持.xls和.xlsx格式
- 按sheet名称识别数据类型

### 数据验证
- 必填字段验证
- 数据格式验证（日期、金额、数量）
- 门店名称映射验证

### 性能优化
- 批量插入数据，提升导入效率
- 事务处理，确保数据一致性
- 内存优化，支持大文件导入

## 测试要点
1. 测试各种Excel格式的兼容性
2. 测试大数据量导入的性能
3. 测试错误数据的处理机制
4. 测试门店名称映射的准确性