<?php
/**
 * 跨店结算汇总表导入调试脚本
 * 用于调试导入失败的问题
 */

require_once __DIR__ . '/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;
use think\facade\Db;
use think\facade\Log;

// 模拟Excel文件路径（请替换为实际的文件路径）
$excelFilePath = './storage/cross/import/test_file.xlsx';

echo "=== 跨店结算汇总表导入调试 ===\n\n";

// 1. 检查文件是否存在
if (!file_exists($excelFilePath)) {
    echo "错误：Excel文件不存在：{$excelFilePath}\n";
    echo "请将测试文件放在正确的位置\n";
    exit;
}

echo "1. 文件检查：✓ 文件存在\n";

try {
    // 2. 读取Excel文件
    $objReader = IOFactory::createReader('Xlsx');
    $objReader->setReadDataOnly(TRUE);
    $objPHPExcel = $objReader->load($excelFilePath);
    
    echo "2. Excel读取：✓ 文件读取成功\n";
    
    // 3. 查找跨店账单汇总表Sheet
    $sheetNames = $objPHPExcel->getSheetNames();
    echo "3. Sheet列表：" . implode(', ', $sheetNames) . "\n";
    
    $targetSheetName = '跨店账单汇总表';
    $sheet = null;
    
    foreach ($sheetNames as $name) {
        if (strpos($name, '汇总') !== false || $name === $targetSheetName) {
            $sheet = $objPHPExcel->getSheetByName($name);
            $targetSheetName = $name;
            break;
        }
    }
    
    if (!$sheet) {
        echo "错误：未找到跨店账单汇总表Sheet\n";
        exit;
    }
    
    echo "4. 目标Sheet：✓ 找到Sheet '{$targetSheetName}'\n";
    
    // 4. 读取Sheet基本信息
    $highestRow = $sheet->getHighestRow();
    $highestColumn = $sheet->getHighestColumn();
    
    echo "5. Sheet信息：总行数 {$highestRow}，总列数 {$highestColumn}\n";
    
    // 5. 读取前几行数据查看表头结构
    echo "\n6. 表头结构分析：\n";
    for ($row = 1; $row <= min(5, $highestRow); $row++) {
        echo "第{$row}行：";
        $rowData = [];
        for ($col = 'A'; $col <= $highestColumn; $col++) {
            $cellValue = $sheet->getCell($col . $row)->getCalculatedValue();
            $cellValue = trim(strval($cellValue ?? ''));
            if (!empty($cellValue)) {
                $rowData[] = $col . ':' . $cellValue;
            }
        }
        echo implode(' | ', $rowData) . "\n";
    }
    
    // 6. 从第3行开始读取数据（跳过前两行表头）
    echo "\n7. 数据读取测试（从第3行开始）：\n";
    $dataCount = 0;
    $sampleData = [];
    
    for ($row = 3; $row <= min(10, $highestRow); $row++) {
        $rowData = [];
        $hasData = false;
        
        for ($col = 'A'; $col <= $highestColumn; $col++) {
            $cellValue = $sheet->getCell($col . $row)->getCalculatedValue();
            $cellValue = trim(strval($cellValue ?? ''));
            $rowData[] = $cellValue;
            
            if (!empty($cellValue)) {
                $hasData = true;
            }
        }
        
        if ($hasData) {
            $dataCount++;
            $sampleData[] = $rowData;
            
            // 显示前几行数据
            if (count($sampleData) <= 3) {
                echo "第{$row}行数据：门店=[{$rowData[0]}] 他店=[{$rowData[1]}] 对账金额=[{$rowData[2]}]\n";
            }
        }
    }
    
    echo "   有效数据行数：{$dataCount}\n";
    
    // 7. 测试门店名称映射
    echo "\n8. 门店名称映射测试：\n";
    
    // 模拟获取门店映射（简化版本）
    $storeMapping = [];
    
    // 这里需要连接数据库，如果没有数据库连接，跳过这部分
    try {
        // 假设已经配置了数据库连接
        $mappings = Db::name('store_name_mapping')
            ->field('department_id, oa_name, youzan_name')
            ->select()
            ->toArray();
        
        foreach ($mappings as $mapping) {
            if (!empty($mapping['youzan_name'])) {
                $storeMapping[$mapping['youzan_name']] = $mapping['department_id'];
            }
            if (!empty($mapping['oa_name'])) {
                $storeMapping[$mapping['oa_name']] = $mapping['department_id'];
            }
        }
        
        echo "   门店映射数量：" . count($storeMapping) . "\n";
        
        // 测试前几行数据的门店映射
        foreach ($sampleData as $index => $row) {
            $storeName = $row[0] ?? '';
            $otherStoreName = $row[1] ?? '';
            
            if (!empty($storeName) && $storeName !== '汇总') {
                $storeId = $storeMapping[$storeName] ?? 0;
                echo "   门店 '{$storeName}' -> ID: {$storeId}\n";
            }
            
            if (!empty($otherStoreName) && $otherStoreName !== '汇总') {
                $otherStoreId = $storeMapping[$otherStoreName] ?? 0;
                echo "   他店 '{$otherStoreName}' -> ID: {$otherStoreId}\n";
            }
            
            if ($index >= 2) break; // 只测试前3行
        }
        
    } catch (Exception $e) {
        echo "   数据库连接失败，跳过门店映射测试：" . $e->getMessage() . "\n";
    }
    
    echo "\n=== 调试完成 ===\n";
    echo "如果看到这里，说明Excel文件读取正常。\n";
    echo "请检查：\n";
    echo "1. 门店名称是否在映射表中存在\n";
    echo "2. 数据是否从第3行开始\n";
    echo "3. 是否有'汇总'行被正确跳过\n";
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
    echo "堆栈跟踪：\n" . $e->getTraceAsString() . "\n";
}
