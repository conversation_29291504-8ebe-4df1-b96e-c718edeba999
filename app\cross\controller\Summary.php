<?php
/**
 * @copyright Copyright (c) 2021 仲正堂工作室
 * @license https://opensource.org/licenses/GPL-3.0
 * @link https://www.gougucms.com
 */

declare (strict_types=1);

namespace app\cross\controller;

use app\base\BaseController;
use app\cross\model\CrossStoreSettleSummary;
use app\cross\validate\CrossStoreSettleSummaryCheck;
use think\exception\ValidateException;
use think\facade\Db;
use think\facade\View;
use think\facade\Log;
use think\facade\Session;
use think\App;

class Summary extends BaseController
{
    protected $userRole; // 用户角色：'area_manager'(区域店长), 'store_manager'(店长), 'normal'(普通用户)
    protected $userAreaId; // 用户所属区域ID
    protected $userStoreId; // 用户所属门店ID
    protected $userStoreIds = []; // 用户管理的所有门店ID

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->initUserRole(); // 初始化用户角色和权限
    }

    /**
     * 初始化用户角色和权限
     */
    protected function initUserRole()
    {
        $session_admin = get_config('app.session_admin');
        $uid = Session::get($session_admin);
        $admin = Db::name("admin")->where(['id' => $uid])->find();

        // 默认为普通用户
        $this->userRole = 'normal';
        $this->userAreaId = 0;
        $this->userStoreId = 0;

        if (empty($admin)) {
            return;
        }

        // 查找用户所属门店
        $userStore = Db::name("department")->where([
            ['id', 'in', $admin['did']],
            ['remark', '=', '门店']
        ])->find();

        if (!empty($userStore)) {
            $this->userStoreId = $userStore['id'];

            // 查找门店所属区域
            $userArea = Db::name("department")->where(['id' => $userStore['pid']])->find();
            if (!empty($userArea)) {
                $this->userAreaId = $userArea['id'];
            }

            // 判断是否为店长（门店负责人）
            if ($userStore['leader_id'] == $uid) {
                $this->userRole = 'store_manager';
                $this->userStoreIds = [$this->userStoreId];
            }
        }

        // 判断是否为区域店长（区域负责人）
        $areaLeader = Db::name("department")->where([
            ['leader_id', '=', $uid],
            ['id', '<>', 0]
        ])->find();

        if (!empty($areaLeader)) {
            // 获取该区域下的所有门店
            $areaStores = Db::name("department")->where([
                ['pid', '=', $areaLeader['id']],
                ['remark', '=', '门店']
            ])->column('id');

            if (!empty($areaStores)) {
                $this->userRole = 'area_manager';
                $this->userAreaId = $areaLeader['id'];
                $this->userStoreIds = $areaStores;
            }
        }
    }

    /**
     * 跨店结算汇总列表
     */
    public function index()
    {
        if (request()->isAjax()) {
            $param = get_params();

            // 构建查询条件
            $where = [];

            // 按月份筛选
            if (!empty($param['period'])) {
                $where[] = ['css.period', '=', $param['period']];
            }

            // 按门店筛选
            if (!empty($param['store_ids']) && is_array($param['store_ids'])) {
                $where[] = ['css.store_id', 'in', $param['store_ids']];
            }

            // 按他店筛选
            if (!empty($param['other_store_ids']) && is_array($param['other_store_ids'])) {
                $where[] = ['css.other_store_id', 'in', $param['other_store_ids']];
            }

            // 根据用户角色限制查询条件
            if ($this->userRole == 'area_manager') {
                // 区域店长只能看到自己区域下门店的数据（只要涉及任一门店即可）
                $where[] = function($query) {
                    $query->whereOr([
                        ['css.store_id', 'in', $this->userStoreIds],
                        ['css.other_store_id', 'in', $this->userStoreIds]
                    ]);
                };
            } else if ($this->userRole == 'store_manager') {
                // 店长只能看到自己门店的数据
                $where[] = ['css.store_id', '=', $this->userStoreId];
                $where[] = ['css.other_store_id', '=', $this->userStoreId];
            }

            // 分页查询
            $rows = empty($param['limit']) ? 20 : intval($param['limit']);

            $query = Db::name('cross_store_settle_summary')
                ->alias('css')
                ->join('store_name_mapping m1', 'm1.department_id = css.store_id', 'LEFT')
                ->join('store_name_mapping m2', 'm2.department_id = css.other_store_id', 'LEFT');

            $list = $query->field('css.*, m1.oa_name as store_name, m2.oa_name as other_store_name')
                ->where($where)
                ->order('css.id desc')
                ->paginate($rows, false, ['query' => $param])
                ->each(function ($item) {
                    // 格式化总对账金额
                    $item['total_reconciliation_amount_formatted'] = number_format(floatval($item['total_reconciliation_amount']), 2);

                    // 格式化储值本金部分
                    $item['p_local_consume_foreign_formatted'] = number_format(floatval($item['p_local_consume_foreign']), 2);
                    $item['p_local_refund_foreign_formatted'] = number_format(floatval($item['p_local_refund_foreign']), 2);
                    $item['p_foreign_consume_local_formatted'] = number_format(floatval($item['p_foreign_consume_local']), 2);
                    $item['p_foreign_refund_local_formatted'] = number_format(floatval($item['p_foreign_refund_local']), 2);
                    $item['p_total_amount_formatted'] = number_format(floatval($item['p_total_amount']), 2);
                    $item['p_reconciliation_amount_formatted'] = number_format(floatval($item['p_reconciliation_amount']), 2);

                    // 格式化储值赠金部分
                    $item['b_local_consume_foreign_formatted'] = number_format(floatval($item['b_local_consume_foreign']), 2);
                    $item['b_local_refund_foreign_formatted'] = number_format(floatval($item['b_local_refund_foreign']), 2);
                    $item['b_foreign_consume_local_formatted'] = number_format(floatval($item['b_foreign_consume_local']), 2);
                    $item['b_foreign_refund_local_formatted'] = number_format(floatval($item['b_foreign_refund_local']), 2);
                    $item['b_total_amount_formatted'] = number_format(floatval($item['b_total_amount']), 2);
                    $item['b_reconciliation_amount_formatted'] = number_format(floatval($item['b_reconciliation_amount']), 2);

                    // 格式化次卡部分金额
                    $item['cc_local_upgrade_foreign_amount_formatted'] = number_format(floatval($item['cc_local_upgrade_foreign_amount']), 2);
                    $item['cc_foreign_upgrade_local_amount_formatted'] = number_format(floatval($item['cc_foreign_upgrade_local_amount']), 2);
                    $item['cc_total_amount_formatted'] = number_format(floatval($item['cc_total_amount']), 2);
                    $item['cc_reconciliation_amount_formatted'] = number_format(floatval($item['cc_reconciliation_amount']), 2);

                    // 格式化网店核销部分
                    $item['ol_total_amount_formatted'] = number_format(floatval($item['ol_total_amount']), 2);
                    $item['ol_reconciliation_amount_formatted'] = number_format(floatval($item['ol_reconciliation_amount']), 2);

                    // 确保count字段数据类型一致性，避免LayUI LAY_NUM错误
                    $item['cc_local_consume_foreign_count'] = intval($item['cc_local_consume_foreign_count'] ?? 0);
                    $item['cc_local_refund_foreign_count'] = intval($item['cc_local_refund_foreign_count'] ?? 0);
                    $item['cc_foreign_consume_local_count'] = intval($item['cc_foreign_consume_local_count'] ?? 0);
                    $item['cc_foreign_refund_local_count'] = intval($item['cc_foreign_refund_local_count'] ?? 0);
                    $item['ol_local_redeem_foreign_count'] = intval($item['ol_local_redeem_foreign_count'] ?? 0);
                    $item['ol_local_refund_foreign_count'] = intval($item['ol_local_refund_foreign_count'] ?? 0);
                    $item['ol_foreign_redeem_local_count'] = intval($item['ol_foreign_redeem_local_count'] ?? 0);
                    $item['ol_foreign_refund_local_count'] = intval($item['ol_foreign_refund_local_count'] ?? 0);
                    
                    // 重要：必须返回修改后的数据，否则each()会返回null
                    return $item;
                });

            // 计算合计数据
            $totalRowData = [];
            if (count($list) > 0) {
                $totalQuery = Db::name('cross_store_settle_summary')
                    ->alias('css')
                    ->where($where);

                $totalData = $totalQuery->field([
                    'SUM(total_reconciliation_amount) as total_reconciliation',
                    // 储值本金部分
                    'SUM(p_local_consume_foreign) as total_p_local_consume_foreign',
                    'SUM(p_local_refund_foreign) as total_p_local_refund_foreign',
                    'SUM(p_foreign_consume_local) as total_p_foreign_consume_local',
                    'SUM(p_foreign_refund_local) as total_p_foreign_refund_local',
                    'SUM(p_total_amount) as total_p_amount',
                    'SUM(p_reconciliation_amount) as total_p_reconciliation',
                    // 储值赠金部分
                    'SUM(b_local_consume_foreign) as total_b_local_consume_foreign',
                    'SUM(b_local_refund_foreign) as total_b_local_refund_foreign',
                    'SUM(b_foreign_consume_local) as total_b_foreign_consume_local',
                    'SUM(b_foreign_refund_local) as total_b_foreign_refund_local',
                    'SUM(b_total_amount) as total_b_amount',
                    'SUM(b_reconciliation_amount) as total_b_reconciliation',
                    // 次卡部分
                    'SUM(cc_local_consume_foreign_count) as total_cc_local_consume_foreign_count',
                    'SUM(cc_local_refund_foreign_count) as total_cc_local_refund_foreign_count',
                    'SUM(cc_foreign_consume_local_count) as total_cc_foreign_consume_local_count',
                    'SUM(cc_foreign_refund_local_count) as total_cc_foreign_refund_local_count',
                    'SUM(cc_local_upgrade_foreign_amount) as total_cc_local_upgrade_foreign_amount',
                    'SUM(cc_foreign_upgrade_local_amount) as total_cc_foreign_upgrade_local_amount',
                    'SUM(cc_total_amount) as total_cc_amount',
                    'SUM(cc_reconciliation_amount) as total_cc_reconciliation',
                    // 网店核销部分
                    'SUM(ol_local_redeem_foreign_count) as total_ol_local_redeem_foreign_count',
                    'SUM(ol_local_refund_foreign_count) as total_ol_local_refund_foreign_count',
                    'SUM(ol_foreign_redeem_local_count) as total_ol_foreign_redeem_local_count',
                    'SUM(ol_foreign_refund_local_count) as total_ol_foreign_refund_local_count',
                    'SUM(ol_total_amount) as total_ol_amount',
                    'SUM(ol_reconciliation_amount) as total_ol_reconciliation'
                ])->find();

                $totalRowData = [
                    // 基础字段（为了保持数据结构一致性）
                    'id' => '', // 确保id字段存在，避免LayUI LAY_NUM错误
                    'store_name' => '', // 合计行显示文本由前端 totalRowText 控制
                    'store_type' => '',
                    'other_store_name' => '',
                    'other_store_type' => '',
                    'total_reconciliation_amount_formatted' => number_format(floatval($totalData['total_reconciliation'] ?? 0), 2),
                    // 储值本金部分
                    'p_local_consume_foreign_formatted' => number_format(floatval($totalData['total_p_local_consume_foreign'] ?? 0), 2),
                    'p_local_refund_foreign_formatted' => number_format(floatval($totalData['total_p_local_refund_foreign'] ?? 0), 2),
                    'p_foreign_consume_local_formatted' => number_format(floatval($totalData['total_p_foreign_consume_local'] ?? 0), 2),
                    'p_foreign_refund_local_formatted' => number_format(floatval($totalData['total_p_foreign_refund_local'] ?? 0), 2),
                    'p_total_amount_formatted' => number_format(floatval($totalData['total_p_amount'] ?? 0), 2),
                    'p_reconciliation_amount_formatted' => number_format(floatval($totalData['total_p_reconciliation'] ?? 0), 2),
                    // 储值赠金部分
                    'b_local_consume_foreign_formatted' => number_format(floatval($totalData['total_b_local_consume_foreign'] ?? 0), 2),
                    'b_local_refund_foreign_formatted' => number_format(floatval($totalData['total_b_local_refund_foreign'] ?? 0), 2),
                    'b_foreign_consume_local_formatted' => number_format(floatval($totalData['total_b_foreign_consume_local'] ?? 0), 2),
                    'b_foreign_refund_local_formatted' => number_format(floatval($totalData['total_b_foreign_refund_local'] ?? 0), 2),
                    'b_total_amount_formatted' => number_format(floatval($totalData['total_b_amount'] ?? 0), 2),
                    'b_reconciliation_amount_formatted' => number_format(floatval($totalData['total_b_reconciliation'] ?? 0), 2),
                    // 次卡部分
                    'cc_local_consume_foreign_count' => intval($totalData['total_cc_local_consume_foreign_count'] ?? 0),
                    'cc_local_refund_foreign_count' => intval($totalData['total_cc_local_refund_foreign_count'] ?? 0),
                    'cc_foreign_consume_local_count' => intval($totalData['total_cc_foreign_consume_local_count'] ?? 0),
                    'cc_foreign_refund_local_count' => intval($totalData['total_cc_foreign_refund_local_count'] ?? 0),
                    'cc_local_upgrade_foreign_amount_formatted' => number_format(floatval($totalData['total_cc_local_upgrade_foreign_amount'] ?? 0), 2),
                    'cc_foreign_upgrade_local_amount_formatted' => number_format(floatval($totalData['total_cc_foreign_upgrade_local_amount'] ?? 0), 2),
                    'cc_total_amount_formatted' => number_format(floatval($totalData['total_cc_amount'] ?? 0), 2),
                    'cc_reconciliation_amount_formatted' => number_format(floatval($totalData['total_cc_reconciliation'] ?? 0), 2),
                    // 网店核销部分
                    'ol_local_redeem_foreign_count' => intval($totalData['total_ol_local_redeem_foreign_count'] ?? 0),
                    'ol_local_refund_foreign_count' => intval($totalData['total_ol_local_refund_foreign_count'] ?? 0),
                    'ol_foreign_redeem_local_count' => intval($totalData['total_ol_foreign_redeem_local_count'] ?? 0),
                    'ol_foreign_refund_local_count' => intval($totalData['total_ol_foreign_refund_local_count'] ?? 0),
                    'ol_total_amount_formatted' => number_format(floatval($totalData['total_ol_amount'] ?? 0), 2),
                    'ol_reconciliation_amount_formatted' => number_format(floatval($totalData['total_ol_reconciliation'] ?? 0), 2)
                ];
            }

            // 转换分页数据为数组格式
            $listArray = $list->toArray();

            return [
                'code' => 0,
                'msg' => '',
                'count' => $listArray['total'],
                'data' => $listArray['data'],
                'totalRow' => $totalRowData
            ];
        } else {
            // 传递用户权限信息到前端
            View::assign('userRole', $this->userRole);
            View::assign('userAreaId', $this->userAreaId);
            View::assign('userStoreId', $this->userStoreId);
            View::assign('userStoreIds', $this->userStoreIds);

            // 记录查看跨店结算汇总列表页面日志
            add_log('view', 0, [], '[跨店结算]-[跨店结算汇总表]：查看跨店结算汇总列表页面');
            Log::info('[跨店结算]-[跨店结算汇总表]：用户查看跨店结算汇总列表页面，用户ID：' . $this->uid);
            return view();
        }
    }

    /**
     * 查看跨店结算汇总详情（只读模式）
     */
    public function view()
    {
        $param = get_params();
        $id = isset($param['id']) ? $param['id'] : 0;

        if (empty($id)) {
            return to_assign(1, '参数错误');
        }

        // 获取详情信息
        $query = Db::name('cross_store_settle_summary')
            ->alias('css')
            ->join('store_name_mapping m1', 'm1.department_id = css.store_id', 'LEFT')
            ->join('store_name_mapping m2', 'm2.department_id = css.other_store_id', 'LEFT')
            ->field('css.*, m1.oa_name as store_name, m2.oa_name as other_store_name, css.store_type, css.other_store_type')
            ->where('css.id', $id);

        // 根据用户角色限制查询条件
        if ($this->userRole == 'area_manager') {
            // 区域店长只能查看自己区域下门店的数据
            $query->where(function($query) {
                $query->whereOr([
                    ['css.store_id', 'in', $this->userStoreIds],
                    ['css.other_store_id', 'in', $this->userStoreIds]
                ]);
            });
        } else if ($this->userRole == 'store_manager') {
            // 店长只能查看自己门店的数据
            $query->where(function($query) {
                $query->whereOr([
                    ['css.store_id', '=', $this->userStoreId],
                    ['css.other_store_id', '=', $this->userStoreId]
                ]);
            });
        }

        $detail = $query->find();

        if (empty($detail)) {
            return to_assign(1, '数据不存在或无权限访问');
        }

        View::assign('detail', $detail);

        // 记录查看跨店结算汇总详情日志
        add_log('view', $id, ['period' => $detail['period'], 'store_name' => $detail['store_name'], 'other_store_name' => $detail['other_store_name']], '[跨店结算]-[跨店结算汇总表]：查看跨店结算汇总详情');
        Log::info('[跨店结算]-[跨店结算汇总表]：用户查看跨店结算汇总详情，ID：' . $id . '，用户ID：' . $this->uid);

        return view();
    }

    /**
     * 编辑跨店结算汇总信息
     */
    public function edit()
    {
        $param = get_params();

        if (request()->isPost()) {
            try {
                validate(CrossStoreSettleSummaryCheck::class)->scene('edit')->check($param);
            } catch (ValidateException $e) {
                return to_assign(1, $e->getError());
            }

            $id = intval($param['id']);
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            // 检查记录是否存在
            $existRecord = Db::name('cross_store_settle_summary')->where('id', $id)->find();
            if (empty($existRecord)) {
                return to_assign(1, '记录不存在');
            }

            // 检查门店配对和月份是否重复（排除当前记录）
            if (CrossStoreSettleSummary::checkExists($param['period'], $param['store_id'], $param['other_store_id'], $id)) {
                return to_assign(1, '该门店配对在此月份已存在记录');
            }

            try {
                // 准备更新数据
                $updateData = [
                    'period' => $param['period'],
                    'store_id' => $param['store_id'],
                    'store_type' => $param['store_type'],
                    'other_store_id' => $param['other_store_id'],
                    'other_store_type' => $param['other_store_type'],
                    'total_reconciliation_amount' => $param['total_reconciliation_amount'] ?? 0,

                    // 储值本金部分
                    'p_local_consume_foreign' => $param['p_local_consume_foreign'] ?? 0,
                    'p_local_refund_foreign' => $param['p_local_refund_foreign'] ?? 0,
                    'p_foreign_consume_local' => $param['p_foreign_consume_local'] ?? 0,
                    'p_foreign_refund_local' => $param['p_foreign_refund_local'] ?? 0,
                    'p_total_amount' => $param['p_total_amount'] ?? 0,
                    'p_reconciliation_amount' => $param['p_reconciliation_amount'] ?? 0,

                    // 储值赠金部分
                    'b_local_consume_foreign' => $param['b_local_consume_foreign'] ?? 0,
                    'b_local_refund_foreign' => $param['b_local_refund_foreign'] ?? 0,
                    'b_foreign_consume_local' => $param['b_foreign_consume_local'] ?? 0,
                    'b_foreign_refund_local' => $param['b_foreign_refund_local'] ?? 0,
                    'b_total_amount' => $param['b_total_amount'] ?? 0,
                    'b_reconciliation_amount' => $param['b_reconciliation_amount'] ?? 0,

                    // 次卡部分
                    'cc_local_consume_foreign_count' => $param['cc_local_consume_foreign_count'] ?? 0,
                    'cc_local_refund_foreign_count' => $param['cc_local_refund_foreign_count'] ?? 0,
                    'cc_foreign_consume_local_count' => $param['cc_foreign_consume_local_count'] ?? 0,
                    'cc_foreign_refund_local_count' => $param['cc_foreign_refund_local_count'] ?? 0,
                    'cc_local_upgrade_foreign_amount' => $param['cc_local_upgrade_foreign_amount'] ?? 0,
                    'cc_foreign_upgrade_local_amount' => $param['cc_foreign_upgrade_local_amount'] ?? 0,
                    'cc_total_amount' => $param['cc_total_amount'] ?? 0,
                    'cc_reconciliation_amount' => $param['cc_reconciliation_amount'] ?? 0,

                    // 网店核销部分
                    'ol_local_redeem_foreign_count' => $param['ol_local_redeem_foreign_count'] ?? 0,
                    'ol_local_refund_foreign_count' => $param['ol_local_refund_foreign_count'] ?? 0,
                    'ol_foreign_redeem_local_count' => $param['ol_foreign_redeem_local_count'] ?? 0,
                    'ol_foreign_refund_local_count' => $param['ol_foreign_refund_local_count'] ?? 0,
                    'ol_total_amount' => $param['ol_total_amount'] ?? 0,
                    'ol_reconciliation_amount' => $param['ol_reconciliation_amount'] ?? 0,

                    'update_time' => time()
                ];

                $res = Db::name('cross_store_settle_summary')->where('id', $id)->update($updateData);

                if ($res) {
                    // 记录操作日志
                    add_log('edit', $id, $param, '[跨店结算]-[跨店结算汇总表]：编辑跨店结算汇总信息');
                    Log::info('[跨店结算]-[跨店结算汇总表]：编辑跨店结算汇总信息成功，ID：' . $id . '，用户ID：' . $this->uid);
                    return to_assign(0, '保存成功');
                } else {
                    return to_assign(1, '保存失败');
                }
            } catch (\think\exception\HttpResponseException $e) {
                // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
                throw $e;
            } catch (\Exception $e) {
                Log::error('[跨店结算]-[跨店结算汇总表]：编辑跨店结算汇总信息失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
                return to_assign(1, '保存失败：' . $e->getMessage());
            }
        } else {
            // GET请求，显示编辑表单
            $id = isset($param['id']) ? $param['id'] : 0;
            if (empty($id)) {
                return to_assign(1, '参数错误');
            }

            $detail = CrossStoreSettleSummary::getDetail($id);
            if (empty($detail)) {
                return to_assign(1, '数据不存在');
            }

            // 获取门店列表（只包含门店）
            $storeList = Db::name('department')
                ->where('status', 1)
                ->where('remark', '门店')
                ->select()
                ->toArray();
            
            View::assign('detail', $detail);
            View::assign('store_list', $storeList);

            return view();
        }
    }

    /**
     * 根据用户权限获取门店列表
     */
    public function getStoreList()
    {
        if (request()->isAjax()) {
            $storeQuery = Db::name('department')
                ->where('status', '>=', 0)
                ->where('remark', '门店')
                ->field('id, title')
                ->order('sort asc, id asc');

            // 根据用户角色限制可选门店
            if ($this->userRole == 'area_manager') {
                // 区域店长只能看到自己区域下的门店
                $storeQuery->where('id', 'in', $this->userStoreIds);
            } else if ($this->userRole == 'store_manager') {
                // 店长只能看到自己的门店
                $storeQuery->where('id', '=', $this->userStoreId);
            }

            $storeList = $storeQuery->select()->toArray();

            return to_assign(0, '获取成功', $storeList);
        }
        return to_assign(1, '非法请求');
    }

    /**
     * 上传Excel文件
     */
    public function uploadExcel()
    {
        try {
            $file = request()->file('file');
            if (empty($file)) {
                return to_assign(1, '请选择要上传的文件');
            }

            // 验证文件类型
            $allowedTypes = ['xls', 'xlsx'];
            $fileExtension = strtolower($file->getOriginalExtension());
            if (!in_array($fileExtension, $allowedTypes)) {
                return to_assign(1, '只支持上传.xls和.xlsx格式的文件');
            }

            // 验证文件大小（50MB）
            if ($file->getSize() > 50 * 1024 * 1024) {
                return to_assign(1, '文件大小不能超过50MB');
            }

            // 保存文件
            $saveName = \think\facade\Filesystem::disk('public')->putFile('cross/import', $file);
            
            if ($saveName) {
                // 记录上传日志
                add_log('upload', 0, ['file_name' => $file->getOriginalName(), 'file_path' => $saveName], '[跨店结算]-[跨店结算汇总表]：Excel文件上传成功');
                Log::info('[跨店结算]-[跨店结算汇总表]：Excel文件上传成功，文件名：' . $file->getOriginalName() . '，用户ID：' . $this->uid);
                
                return to_assign(0, '文件上传成功', [
                    'file_path' => $saveName,
                    'original_name' => $file->getOriginalName()
                ]);
            } else {
                return to_assign(1, '文件上传失败');
            }
        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            Log::error('[跨店结算]-[跨店结算汇总表]：Excel文件上传失败，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '文件上传失败：' . $e->getMessage());
        }
    }

    /**
     * 导入有赞平台Excel数据
     */
    public function importYouzanExcel()
    {
        $param = get_params();
        $period = $param['period'] ?? '';
        $filePath = $param['file_path'] ?? '';

        if (empty($period)) {
            return to_assign(1, '请选择导入月份');
        }

        if (empty($filePath)) {
            return to_assign(1, '请上传Excel文件');
        }

        try {
            // 获取文件完整路径
            $publicPath = get_config('filesystem.disks.public.url');
            $fullPath = '.' . $publicPath . '/' . $filePath;

            if (!file_exists($fullPath)) {
                return to_assign(1, '文件不存在，请重新上传');
            }

            // 读取有赞Excel文件的4个Sheet
            $excelData = $this->readYouzanExcelFile($fullPath);

            if (empty($excelData)) {
                return to_assign(1, 'Excel文件为空或格式不正确');
            }

            // 批量导入数据到4个表
            $result = $this->batchImportYouzanData($period, $excelData);

            if ($result['success']) {
                // 记录操作日志
                add_log('import', 0, [
                    'period' => $period, 
                    'detail_count' => $result['detail_count'],
                    'card_detail_count' => $result['card_detail_count'],
                    'online_detail_count' => $result['online_detail_count'],
                    'summary_count' => $result['summary_count']
                ], '[跨店结算]-[跨店结算汇总表]：有赞Excel数据导入成功');
                
                Log::info('[跨店结算]-[跨店结算汇总表]：有赞Excel数据导入成功，月份：' . $period . '，储值明细：' . $result['detail_count'] . '条，次卡明细：' . $result['card_detail_count'] . '条，网店明细：' . $result['online_detail_count'] . '条，汇总表：' . $result['summary_count'] . '条，用户ID：' . $this->uid);
                
                $successMsg = '导入成功！';
                $successMsg .= '储值跨店结算明细：' . $result['detail_count'] . '条，';
                $successMsg .= '次卡跨店结算明细：' . $result['card_detail_count'] . '条，';
                $successMsg .= '网店订单跨店核销明细：' . $result['online_detail_count'] . '条，';
                $successMsg .= '跨店账单汇总：' . $result['summary_count'] . '条';
                
                return to_assign(0, $successMsg);
            } else {
                $errorMsg = '导入失败！';
                if (!empty($result['errors'])) {
                    $errorMsg .= '错误详情：' . implode('；', array_slice($result['errors'], 0, 5));
                    if (count($result['errors']) > 5) {
                        $errorMsg .= '...等' . count($result['errors']) . '个错误';
                    }
                }
                
                Log::error('[跨店结算]-[跨店结算汇总表]：有赞Excel数据导入失败，错误信息：' . $errorMsg . '，用户ID：' . $this->uid);
                return to_assign(1, $errorMsg);
            }

        } catch (\think\exception\HttpResponseException $e) {
            // 重新抛出HttpResponseException，这是to_assign函数正常的响应机制
            throw $e;
        } catch (\Exception $e) {
            Log::error('[跨店结算]-[跨店结算汇总表]：有赞Excel数据导入异常，错误信息：' . $e->getMessage() . '，用户ID：' . $this->uid);
            return to_assign(1, '导入失败：' . $e->getMessage());
        }
    }

    /**
     * 读取有赞Excel文件的4个Sheet
     * @param string $filePath 文件路径
     * @return array
     */
    private function readYouzanExcelFile($filePath)
    {
        try {
            // 判断文件扩展名并创建对应的读取器
            $fileExtendName = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
            if ($fileExtendName == 'xlsx') {
                $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
            } else {
                $objReader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xls');
            }

            // 设置只读模式
            $objReader->setReadDataOnly(true);

            // 读取Excel文件
            $objPHPExcel = $objReader->load($filePath);

            $result = [
                'detail' => [],           // 储值跨店结算明细
                'card_detail' => [],      // 次卡跨店结算明细
                'online_detail' => [],    // 网店订单跨店核销明细
                'summary' => []           // 跨店账单汇总表（仅作参考，不导入）
            ];

            // 定义Sheet名称映射
            $sheetMapping = [
                '储值跨店结算明细' => 'detail',
                '次卡跨店结算明细' => 'card_detail',
                '网店订单跨店核销明细' => 'online_detail',
                '跨店账单汇总表' => 'summary'
            ];

            // 读取每个Sheet的数据
            foreach ($sheetMapping as $sheetName => $dataKey) {
                try {
                    $sheet = $objPHPExcel->getSheetByName($sheetName);
                    if ($sheet) {
                        $result[$dataKey] = $this->readSheetData($sheet, $sheetName);
                    } else {
                        Log::warning('[跨店结算]-[跨店结算汇总表]：未找到Sheet：' . $sheetName);
                    }
                } catch (\Exception $e) {
                    Log::warning('[跨店结算]-[跨店结算汇总表]：读取Sheet失败：' . $sheetName . '，错误：' . $e->getMessage());
                }
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('[跨店结算]-[跨店结算汇总表]：读取有赞Excel文件失败，错误信息：' . $e->getMessage());
            throw new \Exception('读取Excel文件失败：' . $e->getMessage());
        }
    }

    /**
     * 读取单个Sheet的数据
     * @param object $sheet Sheet对象
     * @param string $sheetName Sheet名称
     * @return array
     */
    private function readSheetData($sheet, $sheetName)
    {
        $data = [];
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

        // 从第二行开始读取数据（第一行为表头）
        for ($row = 2; $row <= $highestRow; $row++) {
            $rowData = [];
            $hasData = false;

            // 读取每一列的数据
            for ($col = 'A'; $col <= $highestColumn; $col++) {
                $cellValue = $sheet->getCell($col . $row)->getCalculatedValue();
                // 确保转换为字符串后再trim
                $cellValue = trim(strval($cellValue ?? ''));
                $rowData[] = $cellValue;
                
                if (!empty($cellValue)) {
                    $hasData = true;
                }
            }

            // 跳过空行
            if ($hasData) {
                $data[] = $rowData;
            }
        }

        return $data;
    }

    /**
     * 批量导入有赞数据到4个表
     * @param string $period 月份
     * @param array $excelData Excel数据
     * @return array
     */
    private function batchImportYouzanData($period, $excelData)
    {
        $result = [
            'success' => true,
            'detail_count' => 0,
            'card_detail_count' => 0,
            'online_detail_count' => 0,
            'summary_count' => 0,
            'errors' => []
        ];

        try {
            // 开启事务
            Db::startTrans();

            // 获取门店名称映射
            $storeMapping = $this->getStoreNameMapping();

            // 导入储值跨店结算明细
            if (!empty($excelData['detail'])) {
                $detailResult = $this->importDetailData($period, $excelData['detail'], $storeMapping);
                $result['detail_count'] = $detailResult['count'];
                $result['errors'] = array_merge($result['errors'], $detailResult['errors']);
            }

            // 导入次卡跨店结算明细
            if (!empty($excelData['card_detail'])) {
                $cardDetailResult = $this->importCardDetailData($period, $excelData['card_detail'], $storeMapping);
                $result['card_detail_count'] = $cardDetailResult['count'];
                $result['errors'] = array_merge($result['errors'], $cardDetailResult['errors']);
            }

            // 导入网店订单跨店核销明细
            if (!empty($excelData['online_detail'])) {
                $onlineDetailResult = $this->importOnlineDetailData($period, $excelData['online_detail'], $storeMapping);
                $result['online_detail_count'] = $onlineDetailResult['count'];
                $result['errors'] = array_merge($result['errors'], $onlineDetailResult['errors']);
            }

            // 导入跨店账单汇总表
            if (!empty($excelData['summary'])) {
                Log::info('[跨店结算]-[跨店结算汇总表]：开始导入汇总表数据，数据行数：' . count($excelData['summary']));
                $summaryResult = $this->importSummaryData($period, $excelData['summary'], $storeMapping);
                $result['summary_count'] = $summaryResult['count'];
                $result['errors'] = array_merge($result['errors'], $summaryResult['errors']);
                Log::info('[跨店结算]-[跨店结算汇总表]：汇总表导入完成，成功：' . $summaryResult['count'] . '条，错误：' . count($summaryResult['errors']) . '条');
            } else {
                Log::warning('[跨店结算]-[跨店结算汇总表]：汇总表数据为空');
            }

            // 如果有错误，回滚事务
            if (!empty($result['errors'])) {
                Db::rollback();
                $result['success'] = false;
            } else {
                Db::commit();
            }

        } catch (\Exception $e) {
            Db::rollback();
            $result['success'] = false;
            $result['errors'][] = '导入过程中发生异常：' . $e->getMessage();
        }

        return $result;
    }

    /**
     * 获取门店名称映射
     * @return array
     */
    private function getStoreNameMapping()
    {
        $mappings = Db::name('store_name_mapping')
            ->alias('snm')
            ->field('snm.*, snm.oa_name as oa_store_name')
            ->select()
            ->toArray();

        $result = [];
        foreach ($mappings as $mapping) {
            // 有赞门店名称映射（使用正确的字段名youzan_name）
            if (!empty($mapping['youzan_name'])) {
                $originalName = $mapping['youzan_name'];
                $result[$originalName] = $mapping['department_id'];
                
                // 添加括号转换版本
                $convertedName = $this->convertBrackets($originalName);
                if ($convertedName !== $originalName) {
                    $result[$convertedName] = $mapping['department_id'];
                }
            }
            
            // OA门店名称映射（使用oa_name字段）
            if (!empty($mapping['oa_name'])) {
                $originalName = $mapping['oa_name'];
                $result[$originalName] = $mapping['department_id'];
                
                // 添加括号转换版本
                $convertedName = $this->convertBrackets($originalName);
                if ($convertedName !== $originalName) {
                    $result[$convertedName] = $mapping['department_id'];
                }
            }
            
            // 也支持通过department表的title字段映射
            if (!empty($mapping['oa_store_name'])) {
                $originalName = $mapping['oa_store_name'];
                $result[$originalName] = $mapping['department_id'];
                
                // 添加括号转换版本
                $convertedName = $this->convertBrackets($originalName);
                if ($convertedName !== $originalName) {
                    $result[$convertedName] = $mapping['department_id'];
                }
            }
        }

        return $result;
    }

    /**
     * 转换中英文括号
     * @param string $name 门店名称
     * @return string
     */
    private function convertBrackets($name)
    {
        // 中文括号转英文括号
        $converted = str_replace(['（', '）'], ['(', ')'], $name);
        
        // 如果原来就是英文括号，也尝试转换为中文括号
        if ($converted === $name) {
            $converted = str_replace(['(', ')'], ['（', '）'], $name);
        }
        
        return $converted;
    }

    /**
     * 智能查找门店ID
     * @param string $storeName 门店名称
     * @param array $storeMapping 门店映射数组
     * @return int
     */
    private function findStoreId($storeName, $storeMapping)
    {
        if (empty($storeName)) {
            return 0;
        }

        // 1. 直接匹配
        if (isset($storeMapping[$storeName])) {
            return $storeMapping[$storeName];
        }

        // 2. 括号转换后匹配
        $convertedName = $this->convertBrackets($storeName);
        if (isset($storeMapping[$convertedName])) {
            return $storeMapping[$convertedName];
        }

        // 3. 去除空格后匹配
        $trimmedName = str_replace(' ', '', $storeName);
        foreach ($storeMapping as $mappedName => $storeId) {
            if (str_replace(' ', '', $mappedName) === $trimmedName) {
                return $storeId;
            }
        }

        // 4. 模糊匹配（包含关系）
        foreach ($storeMapping as $mappedName => $storeId) {
            // 去除括号和空格进行模糊匹配
            $cleanStoreName = str_replace(['(', ')', '（', '）', ' '], '', $storeName);
            $cleanMappedName = str_replace(['(', ')', '（', '）', ' '], '', $mappedName);
            
            if (strlen($cleanStoreName) > 3 && strlen($cleanMappedName) > 3) {
                if (strpos($cleanMappedName, $cleanStoreName) !== false || 
                    strpos($cleanStoreName, $cleanMappedName) !== false) {
                    return $storeId;
                }
            }
        }

        return 0;
    }

    /**
     * 导入储值跨店结算明细数据
     * @param string $period 月份
     * @param array $data 数据
     * @param array $storeMapping 门店映射
     * @return array
     */
    private function importDetailData($period, $data, $storeMapping)
    {
        $count = 0;
        $errors = [];
        $batchData = [];
        $batchSize = 100; // 批量插入大小
        $currentTime = time();

        foreach ($data as $index => $row) {
            try {
                $rowNum = $index + 2; // Excel行号（从第2行开始）

                // 解析数据（根据Excel列顺序）
                $settlementType = strval($row[0] ?? '');
                $consumeStoreName = strval($row[1] ?? '');
                $productName = strval($row[2] ?? '');
                $productQuantity = intval($row[3] ?? 0);
                $cardName = strval($row[4] ?? '');
                $settlementAmount = floatval($row[5] ?? 0);
                $settlementStoreName = strval($row[6] ?? '');
                $settlementPrincipalAmount = floatval($row[7] ?? 0);
                $settlementBonusAmount = floatval($row[8] ?? 0);
                $settlementTime = strval($row[9] ?? '');
                $orderNumber = strval($row[10] ?? '');
                $relatedOrderNumber = strval($row[11] ?? '');
                $cardOpenStoreName = strval($row[12] ?? '');
                $customerName = strval($row[13] ?? '');
                $customerMobile = strval($row[14] ?? '');
                $customerBelongStoreName = strval($row[15] ?? '');

                // 验证必填字段
                if (empty($settlementType) || empty($consumeStoreName)) {
                    $errors[] = "储值明细第{$rowNum}行：结算类型和消费门店不能为空";
                    continue;
                }

                // 门店名称映射（支持智能匹配）
                $consumeStoreId = $this->findStoreId($consumeStoreName, $storeMapping);
                $settlementStoreId = $this->findStoreId($settlementStoreName, $storeMapping);
                $cardOpenStoreId = $this->findStoreId($cardOpenStoreName, $storeMapping);
                $customerBelongStoreId = $this->findStoreId($customerBelongStoreName, $storeMapping);

                if ($consumeStoreId == 0) {
                    $errors[] = "储值明细第{$rowNum}行：消费门店'{$consumeStoreName}'未找到映射";
                    continue;
                }

                // 处理结算时间
                $settlementTimeStamp = null;
                if (!empty($settlementTime)) {
                    $timestamp = strtotime($settlementTime);
                    if ($timestamp !== false) {
                        $settlementTimeStamp = date('Y-m-d H:i:s', $timestamp);
                    }
                }

                // 准备批量插入数据
                $batchData[] = [
                    'settlement_type' => $settlementType,
                    'consume_store_id' => $consumeStoreId,
                    'product_name' => $productName,
                    'product_quantity' => $productQuantity,
                    'card_name' => $cardName,
                    'settlement_amount' => $settlementAmount,
                    'settlement_store_id' => $settlementStoreId,
                    'settlement_principal_amount' => $settlementPrincipalAmount,
                    'settlement_bonus_amount' => $settlementBonusAmount,
                    'settlement_time' => $settlementTimeStamp,
                    'order_number' => $orderNumber,
                    'related_order_number' => $relatedOrderNumber,
                    'card_open_store_id' => $cardOpenStoreId,
                    'customer_name' => $customerName,
                    'customer_mobile' => $customerMobile,
                    'customer_belong_store_id' => $customerBelongStoreId,
                    'period' => $period,
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];

                // 达到批量大小时执行插入
                if (count($batchData) >= $batchSize) {
                    Db::name('cross_store_settle_detail')->insertAll($batchData);
                    $count += count($batchData);
                    $batchData = [];
                }

            } catch (\Exception $e) {
                $errors[] = "储值明细第{$rowNum}行导入失败：" . $e->getMessage();
            }
        }

        // 插入剩余数据
        if (!empty($batchData)) {
            Db::name('cross_store_settle_detail')->insertAll($batchData);
            $count += count($batchData);
        }

        return ['count' => $count, 'errors' => $errors];
    }

    /**
     * 导入次卡跨店结算明细数据
     * @param string $period 月份
     * @param array $data 数据
     * @param array $storeMapping 门店映射
     * @return array
     */
    private function importCardDetailData($period, $data, $storeMapping)
    {
        $count = 0;
        $errors = [];
        $batchData = [];
        $batchSize = 100;
        $currentTime = time();

        foreach ($data as $index => $row) {
            try {
                $rowNum = $index + 2;

                $settlementType = strval($row[0] ?? '');
                $consumeStoreName = strval($row[1] ?? '');
                $productName = strval($row[2] ?? '');
                $productQuantity = intval($row[3] ?? 0);
                $cardName = strval($row[4] ?? '');
                $settlementAmount = floatval($row[5] ?? 0);
                $settlementTime = strval($row[6] ?? '');
                $cardOpenStoreName = strval($row[7] ?? '');
                $orderNumber = strval($row[8] ?? '');
                $relatedOrderNumber = strval($row[9] ?? '');
                $customerName = strval($row[10] ?? '');
                $customerMobile = strval($row[11] ?? '');
                $customerBelongStoreName = strval($row[12] ?? '');

                if (empty($settlementType) || empty($consumeStoreName)) {
                    $errors[] = "次卡明细第{$rowNum}行：结算类型和消费门店不能为空";
                    continue;
                }

                $consumeStoreId = $this->findStoreId($consumeStoreName, $storeMapping);
                $cardOpenStoreId = $this->findStoreId($cardOpenStoreName, $storeMapping);
                $customerBelongStoreId = $this->findStoreId($customerBelongStoreName, $storeMapping);

                if ($consumeStoreId == 0) {
                    $errors[] = "次卡明细第{$rowNum}行：消费门店'{$consumeStoreName}'未找到映射";
                    continue;
                }

                $settlementTimeStamp = null;
                if (!empty($settlementTime)) {
                    $timestamp = strtotime($settlementTime);
                    if ($timestamp !== false) {
                        $settlementTimeStamp = date('Y-m-d H:i:s', $timestamp);
                    }
                }

                $batchData[] = [
                    'settlement_type' => $settlementType,
                    'consume_store_id' => $consumeStoreId,
                    'product_name' => $productName,
                    'product_quantity' => $productQuantity,
                    'card_name' => $cardName,
                    'settlement_amount' => $settlementAmount,
                    'settlement_time' => $settlementTimeStamp,
                    'card_open_store_id' => $cardOpenStoreId,
                    'order_number' => $orderNumber,
                    'related_order_number' => $relatedOrderNumber,
                    'customer_name' => $customerName,
                    'customer_mobile' => $customerMobile,
                    'customer_belong_store_id' => $customerBelongStoreId,
                    'period' => $period,
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];

                if (count($batchData) >= $batchSize) {
                    Db::name('cross_store_settle_card_detail')->insertAll($batchData);
                    $count += count($batchData);
                    $batchData = [];
                }

            } catch (\Exception $e) {
                $errors[] = "次卡明细第{$rowNum}行导入失败：" . $e->getMessage();
            }
        }

        if (!empty($batchData)) {
            Db::name('cross_store_settle_card_detail')->insertAll($batchData);
            $count += count($batchData);
        }

        return ['count' => $count, 'errors' => $errors];
    }

    /**
     * 导入网店订单跨店核销明细数据
     * @param string $period 月份
     * @param array $data 数据
     * @param array $storeMapping 门店映射
     * @return array
     */
    private function importOnlineDetailData($period, $data, $storeMapping)
    {
        $count = 0;
        $errors = [];
        $batchData = [];
        $batchSize = 100;
        $currentTime = time();

        foreach ($data as $index => $row) {
            try {
                $rowNum = $index + 2;

                $settlementType = strval($row[0] ?? '');
                $verificationStoreName = strval($row[1] ?? '');
                $productName = strval($row[2] ?? '');
                $productQuantity = intval($row[3] ?? 0);
                $settlementAmount = floatval($row[4] ?? 0);
                $principalPaymentAmount = floatval($row[5] ?? 0);
                $bonusPaymentAmount = floatval($row[6] ?? 0);
                $cashPaymentAmount = floatval($row[7] ?? 0);
                $cardPaymentAmount = floatval($row[8] ?? 0);
                $settlementTime = strval($row[9] ?? '');
                $orderStoreName = strval($row[10] ?? '');
                $verificationOrderNumber = strval($row[11] ?? '');
                $purchaseOrderNumber = strval($row[12] ?? '');
                $customerName = strval($row[13] ?? '');
                $customerMobile = strval($row[14] ?? '');
                $customerBelongStoreName = strval($row[15] ?? '');

                if (empty($settlementType) || empty($verificationStoreName)) {
                    $errors[] = "网店明细第{$rowNum}行：结算类型和核销门店不能为空";
                    continue;
                }

                $verificationStoreId = $this->findStoreId($verificationStoreName, $storeMapping);
                $orderStoreId = $this->findStoreId($orderStoreName, $storeMapping);
                $customerBelongStoreId = $this->findStoreId($customerBelongStoreName, $storeMapping);

                if ($verificationStoreId == 0) {
                    $errors[] = "网店明细第{$rowNum}行：核销门店'{$verificationStoreName}'未找到映射";
                    continue;
                }

                $settlementTimeStamp = null;
                if (!empty($settlementTime)) {
                    $timestamp = strtotime($settlementTime);
                    if ($timestamp !== false) {
                        $settlementTimeStamp = date('Y-m-d H:i:s', $timestamp);
                    }
                }

                $batchData[] = [
                    'settlement_type' => $settlementType,
                    'verification_store_id' => $verificationStoreId,
                    'product_name' => $productName,
                    'product_quantity' => $productQuantity,
                    'settlement_amount' => $settlementAmount,
                    'principal_payment_amount' => $principalPaymentAmount,
                    'bonus_payment_amount' => $bonusPaymentAmount,
                    'cash_payment_amount' => $cashPaymentAmount,
                    'card_payment_amount' => $cardPaymentAmount,
                    'settlement_time' => $settlementTimeStamp,
                    'order_store_id' => $orderStoreId,
                    'verification_order_number' => $verificationOrderNumber,
                    'purchase_order_number' => $purchaseOrderNumber,
                    'customer_name' => $customerName,
                    'customer_mobile' => $customerMobile,
                    'customer_belong_store_id' => $customerBelongStoreId,
                    'period' => $period,
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];

                if (count($batchData) >= $batchSize) {
                    Db::name('cross_store_settle_online_detail')->insertAll($batchData);
                    $count += count($batchData);
                    $batchData = [];
                }

            } catch (\Exception $e) {
                $errors[] = "网店明细第{$rowNum}行导入失败：" . $e->getMessage();
            }
        }

        if (!empty($batchData)) {
            Db::name('cross_store_settle_online_detail')->insertAll($batchData);
            $count += count($batchData);
        }

        return ['count' => $count, 'errors' => $errors];
    }

    /**
     * 导入跨店账单汇总数据
     * @param string $period 月份
     * @param array $data 数据
     * @param array $storeMapping 门店映射
     * @return array
     */
    private function importSummaryData($period, $data, $storeMapping)
    {
        $count = 0;
        $errors = [];
        $batchData = [];
        $batchSize = 100;
        $currentTime = time();
        $currentStore = ''; // 当前门店（处理合并单元格）

        Log::info('[跨店结算]-[跨店结算汇总表]：汇总表原始数据行数：' . count($data));
        
        // 跳过前两行表头，从第3行开始处理数据
        for ($i = 2; $i < count($data); $i++) {
            try {
                $row = $data[$i];
                $rowNum = $i + 1; // Excel行号（从第1行开始计算）

                // 解析数据
                $storeName = strval($row[0] ?? '');
                $otherStoreName = strval($row[1] ?? '');
                
                Log::info('[跨店结算]-[跨店结算汇总表]：处理第' . $rowNum . '行，门店：' . $storeName . '，他店：' . $otherStoreName);
                
                // 处理合并单元格：如果当前行的门店名称为空，使用上一行的门店名称
                if (!empty($storeName)) {
                    $currentStore = $storeName;
                } else {
                    $storeName = $currentStore;
                }

                // 跳过汇总行和空行
                if (empty($otherStoreName) || $storeName === '汇总' || $otherStoreName === '汇总') {
                    Log::info('[跨店结算]-[跨店结算汇总表]：跳过第' . $rowNum . '行（汇总行或空行）');
                    continue;
                }

                // 解析各项数据
                $totalReconciliationAmount = floatval($row[2] ?? 0);
                
                // 储值本金部分 (列3-8)
                $pLocalConsumeForeign = floatval($row[3] ?? 0);
                $pLocalRefundForeign = floatval($row[4] ?? 0);
                $pForeignConsumeLocal = floatval($row[5] ?? 0);
                $pForeignRefundLocal = floatval($row[6] ?? 0);
                $pTotalAmount = floatval($row[7] ?? 0);
                $pReconciliationAmount = floatval($row[8] ?? 0);
                
                // 储值赠金部分 (列9-14)
                $bLocalConsumeForeign = floatval($row[9] ?? 0);
                $bLocalRefundForeign = floatval($row[10] ?? 0);
                $bForeignConsumeLocal = floatval($row[11] ?? 0);
                $bForeignRefundLocal = floatval($row[12] ?? 0);
                $bTotalAmount = floatval($row[13] ?? 0);
                $bReconciliationAmount = floatval($row[14] ?? 0);
                
                // 次卡部分 (列15-22)
                $ccLocalConsumeForeignCount = intval($row[15] ?? 0);
                $ccLocalRefundForeignCount = intval($row[16] ?? 0);
                $ccForeignConsumeLocalCount = intval($row[17] ?? 0);
                $ccForeignRefundLocalCount = intval($row[18] ?? 0);
                $ccLocalUpgradeForeignAmount = floatval($row[19] ?? 0);
                $ccForeignUpgradeLocalAmount = floatval($row[20] ?? 0);
                $ccTotalAmount = floatval($row[21] ?? 0);
                $ccReconciliationAmount = floatval($row[22] ?? 0);
                
                // 网店核销部分 (列23-28)
                $olLocalRedeemForeignCount = intval($row[23] ?? 0);
                $olLocalRefundForeignCount = intval($row[24] ?? 0);
                $olForeignRedeemLocalCount = intval($row[25] ?? 0);
                $olForeignRefundLocalCount = intval($row[26] ?? 0);
                $olTotalAmount = floatval($row[27] ?? 0);
                $olReconciliationAmount = floatval($row[28] ?? 0);

                // 门店名称映射
                $storeId = $this->findStoreId($storeName, $storeMapping);
                $otherStoreId = $this->findStoreId($otherStoreName, $storeMapping);

                if ($storeId == 0) {
                    $errors[] = "汇总表第{$rowNum}行：门店'{$storeName}'未找到映射";
                    continue;
                }

                if ($otherStoreId == 0) {
                    $errors[] = "汇总表第{$rowNum}行：他店'{$otherStoreName}'未找到映射";
                    continue;
                }

                // 判断门店类型（这里简化处理，可以根据实际业务逻辑调整）
                $storeType = '新店'; // 默认为新店，可以根据业务规则调整
                $otherStoreType = '新店'; // 默认为新店，可以根据业务规则调整

                // 准备批量插入数据
                $batchData[] = [
                    'period' => $period,
                    'store_id' => $storeId,
                    'store_type' => $storeType,
                    'other_store_id' => $otherStoreId,
                    'other_store_type' => $otherStoreType,
                    'total_reconciliation_amount' => $totalReconciliationAmount,
                    
                    // 储值本金部分
                    'p_local_consume_foreign' => $pLocalConsumeForeign,
                    'p_local_refund_foreign' => $pLocalRefundForeign,
                    'p_foreign_consume_local' => $pForeignConsumeLocal,
                    'p_foreign_refund_local' => $pForeignRefundLocal,
                    'p_total_amount' => $pTotalAmount,
                    'p_reconciliation_amount' => $pReconciliationAmount,
                    
                    // 储值赠金部分
                    'b_local_consume_foreign' => $bLocalConsumeForeign,
                    'b_local_refund_foreign' => $bLocalRefundForeign,
                    'b_foreign_consume_local' => $bForeignConsumeLocal,
                    'b_foreign_refund_local' => $bForeignRefundLocal,
                    'b_total_amount' => $bTotalAmount,
                    'b_reconciliation_amount' => $bReconciliationAmount,
                    
                    // 次卡部分
                    'cc_local_consume_foreign_count' => $ccLocalConsumeForeignCount,
                    'cc_local_refund_foreign_count' => $ccLocalRefundForeignCount,
                    'cc_foreign_consume_local_count' => $ccForeignConsumeLocalCount,
                    'cc_foreign_refund_local_count' => $ccForeignRefundLocalCount,
                    'cc_local_upgrade_foreign_amount' => $ccLocalUpgradeForeignAmount,
                    'cc_foreign_upgrade_local_amount' => $ccForeignUpgradeLocalAmount,
                    'cc_total_amount' => $ccTotalAmount,
                    'cc_reconciliation_amount' => $ccReconciliationAmount,
                    
                    // 网店核销部分
                    'ol_local_redeem_foreign_count' => $olLocalRedeemForeignCount,
                    'ol_local_refund_foreign_count' => $olLocalRefundForeignCount,
                    'ol_foreign_redeem_local_count' => $olForeignRedeemLocalCount,
                    'ol_foreign_refund_local_count' => $olForeignRefundLocalCount,
                    'ol_total_amount' => $olTotalAmount,
                    'ol_reconciliation_amount' => $olReconciliationAmount,
                    
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];

                // 达到批量大小时执行插入
                if (count($batchData) >= $batchSize) {
                    Db::name('cross_store_settle_summary')->insertAll($batchData);
                    $count += count($batchData);
                    $batchData = [];
                }

            } catch (\Exception $e) {
                $errors[] = "汇总表第{$rowNum}行导入失败：" . $e->getMessage();
            }
        }

        // 插入剩余数据
        if (!empty($batchData)) {
            Db::name('cross_store_settle_summary')->insertAll($batchData);
            $count += count($batchData);
        }

        return ['count' => $count, 'errors' => $errors];
    }
}
