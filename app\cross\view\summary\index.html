{extend name="../../base/view/common/base" /}

{block name="style"}
<style>
    .layui-form-item {
        margin-bottom: 0;
        display: inline-block;
        vertical-align: top;
    }

    .layui-form-item .layui-inline {
        margin-bottom: 0;
        margin-right: 5px;
    }

    .layui-input-block {
        min-height: 24px;
    }

    .gg-form-bar .layui-input-inline{
        margin-bottom: 0;
        margin-right: 5px;
        vertical-align: top;
    }

    /* 多选下拉样式 */
    .layui-form-select dl {
        min-width: 100%;
    }
    .layui-form-select dl dd.layui-this {
        background-color: #5FB878;
        color: #fff;
    }

    .label-content {
        max-height: 34px !important;
        width: 125px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .xm-select {
        height: 34px !important;
        line-height: 34px !important;
    }

    /* 按钮组样式 */
    .filter-button-group .layui-btn {
        display: inline-block !important;
        vertical-align: top;
        margin-left: 5px;
    }
    .filter-button-group .layui-btn:first-child {
        margin-left: 0;
    }

    /* 合计行样式 */
    .layui-table-total {
        background-color: #f8f8f8;
        font-weight: bold;
    }

    .layui-table-total td {
        border-top: 2px solid #e6e6e6;
    }

    /* 表头分组样式 - 注释掉可能影响列宽的样式 */
    /*
    .layui-table thead tr th {
        background-color: #f0f0f0;
        font-weight: bold;
        border: 1px solid #d0d0d0;
        text-align: center;
    }
    */

    /* 表格样式优化 - 注释掉可能影响列宽的样式 */
    /*
    .layui-table th {
        text-align: center;
        font-weight: bold;
        background-color: #f8f8f8;
        border: 1px solid #e6e6e6;
    }

    .layui-table td {
        text-align: center;
        border: 1px solid #e6e6e6;
    }
    */

    /* 数字字体样式优化 - 注释掉可能影响列宽的样式 */
    /*
    .layui-table td[data-field*="amount"],
    .layui-table td[data-field*="count"],
    .layui-table td[data-field*="total"],
    .layui-table td[data-field*="reconciliation"] {
        font-weight: bold;
    }

    .layui-table td[data-field*="amount"],
    .layui-table td[data-field*="total"],
    .layui-table td[data-field*="reconciliation"] {
        text-align: right;
        padding-right: 12px;
    }

    .layui-table td[data-field*="count"] {
        text-align: right;
        padding-right: 12px;
    }

    .layui-table tbody tr td:nth-child(n+5) {
        font-weight: bold;
    }
    */

    /* 合计行数字样式 */
    .layui-table-total td {
        font-weight: bold;
    }
</style>
{/block}

{block name="body"}
<div class="p-3" style="height:100%; box-sizing: border-box;">
    <div class="body-table" style="overflow:hidden;">
        <!-- 搜索表单 -->
        <form class="layui-form gg-form-bar border-t border-x" lay-filter="webform" style="padding-bottom:10px;">
            <div class="layui-form-item" style="margin-bottom: 10px">
                <input type="text" name="period" id="period"
                       placeholder="请选择汇总月份"
                       autocomplete="off" class="layui-input">
            </div>
            <div id="store-select-container" class="layui-input-inline" style="width:200px; height: 38px;"></div>
            <div id="other-store-select-container" class="layui-input-inline" style="width:200px; height: 38px;"></div>
            <!-- 按钮组 -->
            <div class="layui-input-inline filter-button-group" style="width: auto; white-space: nowrap;">
                <button class="layui-btn layui-btn-normal" lay-submit="" lay-filter="webform"><i
                        class="layui-icon layui-icon-search mr-1"></i>搜索
                </button>
                <button type="button" class="layui-btn layui-btn-primary" id="resetBtn"><i
                        class="layui-icon layui-icon-refresh mr-1"></i>重置
                </button>
            </div>
        </form>
        <!-- 增加外边距，方便页面布局更美观 -->
        <div style="margin: 10px 0;">
            <span style="font-weight:600">跨店结算汇总表</span>
            <div id="periodDescription" style="font-weight:600; color: #F44336; font-size:12px;">月份说明：举例如2025-07指的是2025年6月26日至2025年7月25日期间的数据</div>
        </div>
        <!-- 数据表格 -->
        <table class="layui-hide" id="dataTable" lay-filter="dataTable"></table>
    </div>
</div>

<!-- 表格工具栏模板 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-normal layui-btn-sm" lay-event="import">
            <i class="layui-icon layui-icon-upload"></i> 导入有赞数据
        </button>
    </div>
</script>

<!-- Excel导入弹窗 -->
<div id="importModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="importForm">
        <div class="layui-form-item">
            <label class="layui-form-label" style="color: red;">*导入月份</label>
            <div class="layui-input-block">
                <input type="text" name="import_period" id="import_period"
                       placeholder="请选择导入月份"
                       autocomplete="off" class="layui-input" lay-verify="required">
                <div id="importPeriodDescription" style="color: #F44336; font-size: 12px; margin-top: 5px;">
                    月份说明：举例如2025-07指的是2025年6月26日至2025年7月25日期间的数据
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="color: red;">*Excel文件</label>
            <div class="layui-input-block">
                <button type="button" class="layui-btn" id="uploadExcelBtn">
                    <i class="layui-icon layui-icon-upload"></i>选择文件
                </button>
                <div id="uploadResult" style="margin-top: 10px; color: #666;"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <div style="background-color: #f8f8f8; padding: 10px; border-radius: 4px; margin-bottom: 10px;">
                    <div style="font-weight: bold; color: #333; margin-bottom: 5px;">有赞平台Excel文件格式要求：</div>
                    <div style="color: #666; font-size: 12px;">
                        • 必须包含4个Sheet：储值跨店结算明细、次卡跨店结算明细、网店订单跨店核销明细、跨店账单汇总表<br>
                        • 每个Sheet的第一行为表头，从第二行开始为数据<br>
                        • 支持.xls和.xlsx格式<br>
                        • 门店名称将通过门店名称映射表进行匹配
                    </div>
                    <div style="margin-top: 8px;">
                        <div style="font-weight: bold; color: #333; margin-bottom: 5px;">导入说明：</div>
                        <div style="color: #666; font-size: 12px;">
                            • 系统将自动读取4个Sheet的数据并导入到对应的数据库表<br>
                            • 跨店账单汇总表数据不会导入，仅作为参考<br>
                            • 导入前请确保门店名称映射表已配置完整
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="submitImport">确认导入</button>
                <button type="button" class="layui-btn layui-btn-primary" id="cancelImport">取消</button>
            </div>
        </div>
    </form>
</div>

<!-- 操作列模板 -->
<script type="text/html" id="operationTpl">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="view" title="查看详情">查看</a>
        <a class="layui-btn layui-btn-xs" lay-event="edit" title="编辑">编辑</a>
    </div>
</script>
{/block}

<!-- 脚本 -->
{block name="script"}
<script>
    const moduleInit = ['tool', 'tablePlus', 'upload'];
    function gouguInit() {
        var table = layui.tablePlus, form = layui.form, tool = layui.tool, laydate = layui.laydate;

        // 根据月份生成日期区间说明的函数
        function generatePeriodDescription(period) {
            if (!period || period.length !== 7) {
                return '月份说明：举例如2025-07指的是2025年6月26日至2025年7月25日期间的数据';
            }

            var year = parseInt(period.substring(0, 4));
            var month = parseInt(period.substring(5, 7));

            // 计算开始日期（上月26日）
            var startMonth = month - 1;
            var startYear = year;
            if (startMonth === 0) {
                startMonth = 12;
                startYear = year - 1;
            }

            // 计算结束日期（当月25日）
            var endMonth = month;
            var endYear = year;

            // 月份名称数组
            var monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

            return '月份说明：' + period + '指的是' + startYear + '年' + monthNames[startMonth] + '26日至' + endYear + '年' + monthNames[endMonth] + '25日期间的数据';
        }

        // 更新月份说明
        function updatePeriodDescription(period) {
            $('#periodDescription').text(generatePeriodDescription(period));
        }

        // 初始化月份选择器
        laydate.render({
            elem: '#period',
            type: 'month',
            value: '{:date("Y-m")}',
            format: 'yyyy-MM',
            done: function(value, date, endDate) {
                // 更新月份说明
                updatePeriodDescription(value);
            }
        });

        // 初始化时更新月份说明
        updatePeriodDescription('{:date("Y-m")}');

        // 门店多选组件
        var storeMultiSelect = xmSelect.render({
            el: '#store-select-container',
            name: 'store_ids',
            language: 'zn',
            filterable: true,
            tips: '请选择门店',
            data: [],
            model: { label: { type: 'xm-select-count', max: 0 } },
            prop: {
                name: 'title',
                value: 'id'
            },
            placeholder: '全部门店',
            on: function(data){
                // 门店选择变化时不立即刷新
            }
        });

        // 他店多选组件
        var otherStoreMultiSelect = xmSelect.render({
            el: '#other-store-select-container',
            name: 'other_store_ids',
            language: 'zn',
            filterable: true,
            tips: '请选择他店',
            data: [],
            model: { label: { type: 'xm-select-count', max: 0 } },
            prop: {
                name: 'title',
                value: 'id'
            },
            placeholder: '全部他店',
            on: function(data){
                // 他店选择变化时不立即刷新
            }
        });

        // 加载门店列表
        function loadStoreList() {
            $.ajax({
                url: '/cross/summary/getStoreList',
                type: 'post',
                dataType: 'json',
                success: function(res) {
                    if (res.code == 0) {
                        var storesForXmSelect = [];
                        if (res.data && res.data.length > 0) {
                            storesForXmSelect = res.data.map(function(item) {
                                return { id: item.id, title: item.title };
                            });
                        }
                        storeMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                        otherStoreMultiSelect.update({
                            data: storesForXmSelect,
                            autoRow: true
                        });
                    } else {
                        layer.msg(res.msg || '获取门店列表失败', {icon: 2});
                    }
                },
                error: function(){
                    layer.msg('获取门店列表失败', {icon:2});
                }
            });
        }

        // 初始化加载门店列表
        loadStoreList();

        // 生成多级表头配置
        function generateTableCols() {
            // 第一级表头 - 恢复固定列配置
            var firstRow = [
                {field: 'store_name', title: '门店', width: 100, align: 'center', totalRowText: '合计：', fixed: 'left', rowspan: 2},
                {field: 'store_type', title: '门店类型', width: 80, align: 'center', fixed: 'left', rowspan: 2},
                {field: 'other_store_name', title: '他店', width: 100, align: 'center', fixed: 'left', rowspan: 2},
                {field: 'other_store_type', title: '他店类型', width: 80, align: 'center', fixed: 'left', rowspan: 2},
                {field: 'total_reconciliation_amount_formatted', title: '总对账金额', width: 110, align: 'center', fixed: 'left', rowspan: 2, totalRow: true},
                {title: '储值本金', colspan: 6, align: 'center'},
                {title: '储值赠金', colspan: 6, align: 'center'},
                {title: '次卡', colspan: 8, align: 'center'},
                {title: '网店核销', colspan: 6, align: 'center'},
                {title: '操作', width: 100, align: 'center', fixed: 'right', rowspan: 2, toolbar: '#operationTpl'}
            ];

            // 第二级表头
            var secondRow = [];

            // 储值本金的第二级表头
            secondRow.push(
                {field: 'p_local_consume_foreign_formatted', title: '本店耗他店本金', width: 130, align: 'center', totalRow: true},
                {field: 'p_local_refund_foreign_formatted', title: '本店回退他店本金', width: 140, align: 'center', totalRow: true},
                {field: 'p_foreign_consume_local_formatted', title: '他店耗本店本金', width: 140, align: 'center', totalRow: true},
                {field: 'p_foreign_refund_local_formatted', title: '他店回退本店本金', width: 140, align: 'center', totalRow: true},
                {field: 'p_total_amount_formatted', title: '本金合计', width: 100, align: 'center', totalRow: true},
                {field: 'p_reconciliation_amount_formatted', title: '本金对账金额', width: 120, align: 'center', totalRow: true}
            );

            // 储值赠金的第二级表头
            secondRow.push(
                {field: 'b_local_consume_foreign_formatted', title: '本店耗他店赠金', width: 130, align: 'center', totalRow: true},
                {field: 'b_local_refund_foreign_formatted', title: '本店回退他店赠金', width: 140, align: 'center', totalRow: true},
                {field: 'b_foreign_consume_local_formatted', title: '他店耗本店赠金', width: 140, align: 'center', totalRow: true},
                {field: 'b_foreign_refund_local_formatted', title: '他店回退本店赠金', width: 140, align: 'center', totalRow: true},
                {field: 'b_total_amount_formatted', title: '赠金合计', width: 100, align: 'center', totalRow: true},
                {field: 'b_reconciliation_amount_formatted', title: '赠金对账金额', width: 120, align: 'center', totalRow: true}
            );

            // 次卡的第二级表头
            secondRow.push(
                {field: 'cc_local_consume_foreign_count', title: '本店耗他店卡(次)', width: 130, align: 'center', totalRow: true},
                {field: 'cc_local_refund_foreign_count', title: '本店回退他店卡(次)', width: 140, align: 'center', totalRow: true},
                {field: 'cc_foreign_consume_local_count', title: '他店耗本店卡(次)', width: 140, align: 'center', totalRow: true},
                {field: 'cc_foreign_refund_local_count', title: '他店回退本店卡(次)', width: 140, align: 'center', totalRow: true},
                {field: 'cc_local_upgrade_foreign_amount_formatted', title: '本店升他店卡(金额)', width: 150, align: 'center', totalRow: true},
                {field: 'cc_foreign_upgrade_local_amount_formatted', title: '他店升本店卡(金额)', width: 150, align: 'center', totalRow: true},
                {field: 'cc_total_amount_formatted', title: '次卡金额合计', width: 120, align: 'center', totalRow: true},
                {field: 'cc_reconciliation_amount_formatted', title: '次卡对账金额', width: 120, align: 'center', totalRow: true}
            );

            // 网店核销的第二级表头
            secondRow.push(
                {field: 'ol_local_redeem_foreign_count', title: '本店核销他店码(次)', width: 140, align: 'center', totalRow: true},
                {field: 'ol_local_refund_foreign_count', title: '本店回退他店码(次)', width: 140, align: 'center', totalRow: true},
                {field: 'ol_foreign_redeem_local_count', title: '他店核销本店码(次)', width: 140, align: 'center', totalRow: true},
                {field: 'ol_foreign_refund_local_count', title: '他店回退本店码(次)', width: 140, align: 'center', totalRow: true},
                {field: 'ol_total_amount_formatted', title: '网店核销金额合计', width: 140, align: 'center', totalRow: true},
                {field: 'ol_reconciliation_amount_formatted', title: '网店核销对账金额', width: 140, align: 'center', totalRow: true}
            );

            return [firstRow, secondRow];
        }

        // 渲染表格
        try {
            layui.pageTable = table.render({
                elem: '#dataTable',
                toolbar: '#toolbarDemo',
                title: '跨店结算汇总列表',
                page: true,
                limit: 20,
                limits: [20, 50, 100, 500, 1000, 10000],
                height: 'full-150',
                url: "/cross/summary/index",
                loading: true,
                even: true,
                totalRow: true,
                where: {
                    period: '{:date("Y-m")}', // 默认加载当月数据
                    store_ids: [],
                    other_store_ids: []
                },
                parseData: function(res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data,
                        "totalRow": res.totalRow
                    };
                },
                cols: generateTableCols(),
                done: function(res, curr, count) {
                    // 表格渲染完成后的回调
                    if (res.code !== 0) {
                        console.error('表格数据加载失败:', res.msg);
                    }
                }
            });
        } catch (e) {
            console.error('表格渲染失败:', e);
            layer.msg('表格初始化失败，请刷新页面重试', {icon: 2});
        }

        // 监听搜索按钮
        form.on('submit(webform)', function(data) {
            var selectedStoreIds = storeMultiSelect.getValue().map(function(item) { return item.id; });
            var selectedOtherStoreIds = otherStoreMultiSelect.getValue().map(function(item) { return item.id; });

            layui.pageTable.reload({
                where: {
                    period: $('#period').val(),
                    store_ids: selectedStoreIds,
                    other_store_ids: selectedOtherStoreIds
                }
            });
            return false;
        });

        // 监听重置按钮
        $('#resetBtn').on('click', function(){
            $('#period').val('{:date("Y-m")}');
            storeMultiSelect.setValue([]);
            otherStoreMultiSelect.setValue([]);
            form.render();

            // 重置后自动执行搜索，应用当月筛选条件
            layui.pageTable.reload({
                where: {
                    period: '{:date("Y-m")}',
                    store_ids: [],
                    other_store_ids: []
                }
            });
        });

        // 监听头工具栏事件
        table.on('toolbar(dataTable)', function(obj) {
            switch(obj.event) {
                case 'import':
                    openImportModal();
                    break;
            }
        });

        // 监听工具条
        table.on('tool(dataTable)', function(obj) {
            var data = obj.data;
            
            switch(obj.event) {
                case 'view':
                    tool.side('/cross/summary/view?id=' + data.id, '查看跨店结算汇总详情');
                    break;
                case 'edit':
                    tool.side('/cross/summary/edit?id=' + data.id, '编辑跨店结算汇总');
                    break;
            }
        });

        // 监听抽屉关闭事件，刷新表格
        $(document).on('sideClose', function() {
            if (layui.pageTable && layui.pageTable.reload) {
                layui.pageTable.reload();
            }
        });

        // Excel导入功能
        var uploadedFile = null;

        function openImportModal() {
            uploadedFile = null;
            $('#uploadResult').html('');
            layer.open({
                type: 1,
                title: '导入有赞平台Excel数据',
                content: $('#importModal'),
                area: ['650px', '450px'],
                btn: false,
                success: function(layero, index) {
                    form.render();
                    
                    // 初始化月份选择器
                    laydate.render({
                        elem: '#import_period',
                        type: 'month',
                        value: '{:date("Y-m")}',
                        format: 'yyyy-MM',
                        done: function(value, date, endDate) {
                            // 更新导入弹窗的月份说明
                            updateImportPeriodDescription(value);
                        }
                    });

                    // 初始化时更新导入弹窗的月份说明
                    updateImportPeriodDescription('{:date("Y-m")}');
                }
            });
        }

        // 更新导入弹窗的月份说明
        function updateImportPeriodDescription(period) {
            $('#importPeriodDescription').text(generatePeriodDescription(period));
        }

        // 文件上传
        var upload = layui.upload;
        upload.render({
            elem: '#uploadExcelBtn',
            url: '/cross/summary/uploadExcel',
            accept: 'file',
            exts: 'xls|xlsx',
            size: 51200, // 50MB
            before: function(obj) {
                layer.load();
            },
            done: function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    uploadedFile = res.data.file_path;
                    $('#uploadResult').html('<span style="color: green;">文件上传成功：' + res.data.original_name + '</span>');
                } else {
                    $('#uploadResult').html('<span style="color: red;">上传失败：' + res.msg + '</span>');
                }
            },
            error: function() {
                layer.closeAll('loading');
                $('#uploadResult').html('<span style="color: red;">上传失败，请重试</span>');
            }
        });

        // 监听导入表单提交
        form.on('submit(submitImport)', function(data) {
            if (!uploadedFile) {
                layer.msg('请先上传Excel文件', {icon: 2});
                return false;
            }

            var submitData = {
                period: data.field.import_period,
                file_path: uploadedFile
            };

            layer.load();
            $.post('/cross/summary/importYouzanExcel', submitData, function(res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    layer.msg(res.msg, {icon: 1});
                    layer.closeAll();
                    // 刷新表格
                    if (layui.pageTable && layui.pageTable.reload) {
                        layui.pageTable.reload();
                    }
                } else {
                    layer.msg(res.msg, {icon: 2});
                }
            }).fail(function() {
                layer.closeAll('loading');
                layer.msg('请求失败，请重试', {icon: 2});
            });

            return false;
        });

        // 监听取消按钮
        $('#cancelImport').on('click', function() {
            layer.closeAll();
        });
    }
</script>
{/block}
